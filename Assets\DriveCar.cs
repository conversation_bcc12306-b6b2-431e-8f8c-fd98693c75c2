using UnityEngine;

public class DriveCar : MonoBehaviour
{
    [Header("Car Settings")]
    public float moveSpeed = 5f;
    public float turnSpeed = 2f;
    public Transform car;
    public Transform steeringWheel;
    public float maxSteeringAngle = 30f;
    public float steeringWheelTurnSpeed = 5f;
    public Transform[] frontWheels;
    public Transform[] backWheels;
    public float wheelTurnSpeed = 5f;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        
    }
}
