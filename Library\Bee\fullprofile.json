{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 36960, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 36960, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 36960, "tid": 64541, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 36960, "tid": 64541, "ts": 1754159529812753, "dur": 1011, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 36960, "tid": 64541, "ts": 1754159529816576, "dur": 746, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 36960, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 36960, "tid": 1, "ts": 1754159528243112, "dur": 5165, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 36960, "tid": 1, "ts": 1754159528248282, "dur": 36419, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 36960, "tid": 1, "ts": 1754159528284711, "dur": 41503, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 36960, "tid": 64541, "ts": 1754159529817326, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 36960, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528241581, "dur": 3781, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528245365, "dur": 1556747, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528246534, "dur": 2006, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528248546, "dur": 1174, "ph": "X", "name": "ProcessMessages 6183", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528249724, "dur": 350, "ph": "X", "name": "ReadAsync 6183", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250078, "dur": 11, "ph": "X", "name": "ProcessMessages 20500", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250091, "dur": 40, "ph": "X", "name": "ReadAsync 20500", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250132, "dur": 1, "ph": "X", "name": "ProcessMessages 2046", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250134, "dur": 41, "ph": "X", "name": "ReadAsync 2046", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250176, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250192, "dur": 14, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250208, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250225, "dur": 20, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250246, "dur": 14, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250262, "dur": 13, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250277, "dur": 430, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250710, "dur": 95, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250806, "dur": 7, "ph": "X", "name": "ProcessMessages 10551", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250814, "dur": 28, "ph": "X", "name": "ReadAsync 10551", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250843, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250867, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250886, "dur": 13, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250902, "dur": 14, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250918, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250937, "dur": 13, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250952, "dur": 14, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528250968, "dur": 78, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251047, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251062, "dur": 23, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251087, "dur": 62, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251152, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251155, "dur": 31, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251187, "dur": 1, "ph": "X", "name": "ProcessMessages 1116", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251189, "dur": 17, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251208, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251223, "dur": 15, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251240, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251259, "dur": 14, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251275, "dur": 12, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251289, "dur": 19, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251310, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251329, "dur": 12, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251343, "dur": 14, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251359, "dur": 13, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251374, "dur": 35, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251410, "dur": 18, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251431, "dur": 14, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251448, "dur": 13, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251462, "dur": 12, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251476, "dur": 21, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251498, "dur": 19, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251519, "dur": 14, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251535, "dur": 13, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251550, "dur": 13, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251565, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251589, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251610, "dur": 13, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251625, "dur": 26, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251652, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251676, "dur": 19, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251697, "dur": 21, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251720, "dur": 41, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251763, "dur": 16, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251781, "dur": 19, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251801, "dur": 31, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251835, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251837, "dur": 16, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251854, "dur": 14, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251870, "dur": 12, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251884, "dur": 22, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251909, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251934, "dur": 13, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251949, "dur": 39, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528251991, "dur": 13, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252005, "dur": 13, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252020, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252042, "dur": 14, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252060, "dur": 13, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252075, "dur": 15, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252092, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252112, "dur": 12, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252125, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252148, "dur": 36, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252187, "dur": 15, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252203, "dur": 20, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252226, "dur": 14, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252242, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252261, "dur": 17, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252281, "dur": 14, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252297, "dur": 15, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252314, "dur": 13, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252329, "dur": 16, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252347, "dur": 13, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252362, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252378, "dur": 14, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252393, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252409, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252410, "dur": 60, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252472, "dur": 22, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252496, "dur": 12, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252510, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252533, "dur": 44, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252578, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252580, "dur": 15, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252598, "dur": 13, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252612, "dur": 18, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252632, "dur": 64, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252697, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252698, "dur": 14, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252715, "dur": 16, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252733, "dur": 14, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252749, "dur": 12, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252763, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252790, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252807, "dur": 28, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252838, "dur": 14, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252853, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252879, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252896, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252913, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252931, "dur": 14, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252947, "dur": 13, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252961, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252963, "dur": 11, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252976, "dur": 13, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528252991, "dur": 17, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253010, "dur": 15, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253026, "dur": 14, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253043, "dur": 14, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253058, "dur": 12, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253072, "dur": 16, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253091, "dur": 16, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253109, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253126, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253150, "dur": 15, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253168, "dur": 12, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253182, "dur": 15, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253202, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253219, "dur": 15, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253236, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253256, "dur": 38, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253296, "dur": 15, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253313, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253331, "dur": 16, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253349, "dur": 14, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253366, "dur": 14, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253382, "dur": 14, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253398, "dur": 160, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253561, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253580, "dur": 18, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253601, "dur": 15, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253618, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253650, "dur": 15, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253667, "dur": 13, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253682, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253702, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253722, "dur": 22, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253747, "dur": 15, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253764, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253780, "dur": 13, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253795, "dur": 12, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253809, "dur": 15, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253826, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253846, "dur": 14, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253861, "dur": 103, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253966, "dur": 1, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253967, "dur": 29, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253997, "dur": 1, "ph": "X", "name": "ProcessMessages 1344", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528253999, "dur": 21, "ph": "X", "name": "ReadAsync 1344", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254022, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254039, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254057, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254074, "dur": 15, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254091, "dur": 12, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254105, "dur": 16, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254124, "dur": 16, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254142, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254162, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254183, "dur": 24, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254209, "dur": 21, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254232, "dur": 13, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254247, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254248, "dur": 14, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254264, "dur": 18, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254284, "dur": 14, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254300, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254319, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254338, "dur": 13, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254353, "dur": 12, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254368, "dur": 21, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254391, "dur": 17, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254409, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254412, "dur": 15, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254429, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254446, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254461, "dur": 14, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254477, "dur": 13, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254491, "dur": 14, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254507, "dur": 17, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254526, "dur": 13, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254541, "dur": 21, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254564, "dur": 13, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254579, "dur": 16, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254596, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254612, "dur": 42, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254656, "dur": 14, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254672, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254692, "dur": 12, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254705, "dur": 12, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254720, "dur": 13, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254735, "dur": 14, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254752, "dur": 15, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254769, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254788, "dur": 16, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254807, "dur": 13, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254821, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254839, "dur": 40, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254881, "dur": 38, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254922, "dur": 14, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254937, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254952, "dur": 13, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254968, "dur": 20, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528254990, "dur": 17, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255009, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255029, "dur": 13, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255043, "dur": 15, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255060, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255081, "dur": 13, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255096, "dur": 14, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255112, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255129, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255144, "dur": 13, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255159, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255173, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255188, "dur": 43, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255234, "dur": 20, "ph": "X", "name": "ReadAsync 1257", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255256, "dur": 12, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255271, "dur": 12, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255285, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255300, "dur": 16, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255318, "dur": 14, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255334, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255349, "dur": 15, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255366, "dur": 13, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255380, "dur": 26, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255408, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255424, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255439, "dur": 15, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255456, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255471, "dur": 13, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255487, "dur": 14, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255503, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255526, "dur": 14, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255541, "dur": 14, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255558, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255575, "dur": 14, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255593, "dur": 12, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255607, "dur": 12, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255621, "dur": 13, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255637, "dur": 90, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255731, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255734, "dur": 39, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255776, "dur": 2, "ph": "X", "name": "ProcessMessages 2037", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255778, "dur": 21, "ph": "X", "name": "ReadAsync 2037", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255801, "dur": 27, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255830, "dur": 15, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255847, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255865, "dur": 12, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255879, "dur": 107, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528255989, "dur": 31, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256021, "dur": 1, "ph": "X", "name": "ProcessMessages 1866", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256044, "dur": 27, "ph": "X", "name": "ReadAsync 1866", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256072, "dur": 1, "ph": "X", "name": "ProcessMessages 1832", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256074, "dur": 16, "ph": "X", "name": "ReadAsync 1832", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256091, "dur": 17, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256111, "dur": 14, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256127, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256143, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256162, "dur": 14, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256178, "dur": 14, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256194, "dur": 14, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256210, "dur": 13, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256225, "dur": 14, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256241, "dur": 14, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256257, "dur": 13, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256272, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256298, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256314, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256343, "dur": 58, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256403, "dur": 29, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256434, "dur": 13, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256449, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256466, "dur": 13, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256481, "dur": 13, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256495, "dur": 16, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256513, "dur": 52, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256570, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256605, "dur": 1, "ph": "X", "name": "ProcessMessages 1764", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256607, "dur": 17, "ph": "X", "name": "ReadAsync 1764", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256626, "dur": 13, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256641, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256657, "dur": 30, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256689, "dur": 12, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256703, "dur": 10, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256715, "dur": 13, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256730, "dur": 16, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256748, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256764, "dur": 27, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256794, "dur": 12, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256807, "dur": 13, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256821, "dur": 16, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256839, "dur": 13, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256854, "dur": 17, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256872, "dur": 13, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256887, "dur": 13, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256902, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256919, "dur": 11, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256932, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256950, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256965, "dur": 13, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256980, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528256996, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257014, "dur": 13, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257029, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257045, "dur": 15, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257062, "dur": 13, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257077, "dur": 13, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257092, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257111, "dur": 15, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257129, "dur": 13, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257144, "dur": 12, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257158, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257173, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257202, "dur": 19, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257223, "dur": 14, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257238, "dur": 15, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257255, "dur": 12, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257268, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257270, "dur": 14, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257286, "dur": 15, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257303, "dur": 13, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257317, "dur": 15, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257334, "dur": 13, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257349, "dur": 23, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257373, "dur": 30, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257404, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257422, "dur": 28, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257452, "dur": 15, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257468, "dur": 2, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257471, "dur": 14, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257487, "dur": 12, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257501, "dur": 14, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257516, "dur": 14, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257532, "dur": 14, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257548, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257563, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257582, "dur": 13, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257597, "dur": 14, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257612, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257635, "dur": 15, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257653, "dur": 21, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257676, "dur": 13, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257691, "dur": 13, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257706, "dur": 14, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257722, "dur": 32, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257756, "dur": 30, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257788, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257805, "dur": 14, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257821, "dur": 13, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257836, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257853, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257872, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528257892, "dur": 151, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258045, "dur": 41, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258086, "dur": 2, "ph": "X", "name": "ProcessMessages 4334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258089, "dur": 15, "ph": "X", "name": "ReadAsync 4334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258106, "dur": 14, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258122, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258140, "dur": 13, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258154, "dur": 12, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258168, "dur": 13, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258184, "dur": 14, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258200, "dur": 12, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258214, "dur": 14, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258229, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258252, "dur": 13, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258267, "dur": 12, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258280, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258298, "dur": 14, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258314, "dur": 14, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258330, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258344, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258360, "dur": 70, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258431, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258433, "dur": 28, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258462, "dur": 1, "ph": "X", "name": "ProcessMessages 2262", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258463, "dur": 13, "ph": "X", "name": "ReadAsync 2262", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258478, "dur": 13, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258493, "dur": 35, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258531, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258551, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258552, "dur": 13, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258567, "dur": 12, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258581, "dur": 12, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258594, "dur": 15, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258612, "dur": 12, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258625, "dur": 16, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258643, "dur": 27, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258672, "dur": 14, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258689, "dur": 14, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258704, "dur": 12, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258718, "dur": 41, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258761, "dur": 17, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258780, "dur": 14, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258796, "dur": 17, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258814, "dur": 42, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258857, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258858, "dur": 13, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258873, "dur": 13, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258887, "dur": 15, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258904, "dur": 12, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258917, "dur": 14, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258933, "dur": 14, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258949, "dur": 13, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258964, "dur": 13, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258978, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528258996, "dur": 15, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259013, "dur": 13, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259029, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259052, "dur": 13, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259067, "dur": 13, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259082, "dur": 12, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259096, "dur": 16, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259113, "dur": 12, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259127, "dur": 35, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259164, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259179, "dur": 12, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259193, "dur": 13, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259208, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259223, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259296, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259307, "dur": 413, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259729, "dur": 51, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259781, "dur": 2, "ph": "X", "name": "ProcessMessages 4418", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259785, "dur": 44, "ph": "X", "name": "ReadAsync 4418", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259832, "dur": 77, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259914, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259945, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528259947, "dur": 49, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260002, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260039, "dur": 16, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260057, "dur": 65, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260124, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260141, "dur": 13, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260156, "dur": 23, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260181, "dur": 58, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260241, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260257, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260283, "dur": 16, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260301, "dur": 14, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260317, "dur": 57, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260376, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260393, "dur": 18, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260413, "dur": 14, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260429, "dur": 56, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260487, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260510, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260532, "dur": 12, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260546, "dur": 50, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260599, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260615, "dur": 12, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260629, "dur": 15, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260646, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260660, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260719, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260773, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260791, "dur": 39, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260832, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260869, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260888, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528260890, "dur": 121, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261015, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261017, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261070, "dur": 13, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261086, "dur": 29, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261116, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261118, "dur": 50, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261174, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261226, "dur": 1, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261228, "dur": 26, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261259, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261262, "dur": 94, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261361, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261402, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261405, "dur": 20, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261427, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261428, "dur": 77, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261508, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261510, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261549, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261552, "dur": 29, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261584, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261587, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261668, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261702, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261704, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261734, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261736, "dur": 80, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261822, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261872, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261875, "dur": 43, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261920, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261922, "dur": 54, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528261982, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262029, "dur": 2, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262032, "dur": 23, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262058, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262061, "dur": 60, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262124, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262144, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262162, "dur": 17, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262180, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262181, "dur": 77, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262269, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262290, "dur": 15, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262307, "dur": 16, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262326, "dur": 17, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262345, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262347, "dur": 13, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262363, "dur": 69, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262434, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262460, "dur": 210, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262673, "dur": 2, "ph": "X", "name": "ProcessMessages 2352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262677, "dur": 21, "ph": "X", "name": "ReadAsync 2352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262699, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262701, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262727, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262729, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262753, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262755, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262794, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262811, "dur": 49, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262863, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262865, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262888, "dur": 49, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262939, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262941, "dur": 16, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262958, "dur": 1, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262960, "dur": 14, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528262976, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263049, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263072, "dur": 14, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263087, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263090, "dur": 12, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263105, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263169, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263232, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263234, "dur": 38, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263276, "dur": 1, "ph": "X", "name": "ProcessMessages 1891", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263278, "dur": 16, "ph": "X", "name": "ReadAsync 1891", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263296, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263314, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263334, "dur": 56, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263392, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263413, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263432, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263449, "dur": 61, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263513, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263533, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263552, "dur": 14, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263567, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263569, "dur": 48, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263618, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263648, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263669, "dur": 16, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263686, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263688, "dur": 44, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263735, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263760, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263761, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263782, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263804, "dur": 42, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263848, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263870, "dur": 17, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263890, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263909, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263966, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528263986, "dur": 16, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264004, "dur": 16, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264021, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264023, "dur": 18, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264043, "dur": 17, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264063, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264086, "dur": 12, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264100, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264117, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264118, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264175, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264197, "dur": 15, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264215, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264231, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264292, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264312, "dur": 28, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264342, "dur": 19, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264363, "dur": 14, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264379, "dur": 51, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264432, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264450, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264475, "dur": 15, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264493, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264545, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264566, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264589, "dur": 17, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264609, "dur": 15, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264626, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264682, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264700, "dur": 14, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264717, "dur": 13, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264733, "dur": 14, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264750, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264803, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264840, "dur": 23, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264864, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264866, "dur": 14, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264881, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264882, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264935, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264959, "dur": 22, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528264984, "dur": 56, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265043, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265061, "dur": 18, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265083, "dur": 53, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265139, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265141, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265161, "dur": 16, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265179, "dur": 14, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265197, "dur": 14, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265213, "dur": 56, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265274, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265295, "dur": 16, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265313, "dur": 13, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265328, "dur": 49, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265380, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265406, "dur": 58, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265466, "dur": 14, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265483, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265532, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265550, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265568, "dur": 16, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265586, "dur": 12, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265600, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265601, "dur": 51, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265654, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265724, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265726, "dur": 24, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265751, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265753, "dur": 15, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265770, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265794, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265816, "dur": 12, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265830, "dur": 51, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265883, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265933, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528265961, "dur": 41, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266005, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266008, "dur": 46, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266057, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266106, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266108, "dur": 18, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266129, "dur": 17, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266149, "dur": 64, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266215, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266231, "dur": 13, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266246, "dur": 19, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266268, "dur": 15, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266285, "dur": 60, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266347, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266367, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266385, "dur": 14, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266402, "dur": 59, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266463, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266482, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266499, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266517, "dur": 59, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266578, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266596, "dur": 21, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266619, "dur": 12, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266644, "dur": 13, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266661, "dur": 30, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266693, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266710, "dur": 12, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266723, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266740, "dur": 14, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266756, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266813, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266815, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266836, "dur": 14, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266853, "dur": 14, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266869, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266926, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266944, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266964, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266966, "dur": 15, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528266984, "dur": 55, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267043, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267063, "dur": 11, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267077, "dur": 13, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267093, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267160, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267196, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267214, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267231, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267232, "dur": 57, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267292, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267310, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267311, "dur": 43, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267356, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267357, "dur": 14, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267374, "dur": 48, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267425, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267450, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267474, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267476, "dur": 57, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267536, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267554, "dur": 92, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267650, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267653, "dur": 35, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267691, "dur": 2, "ph": "X", "name": "ProcessMessages 1519", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267694, "dur": 15, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267711, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267713, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267765, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267785, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267806, "dur": 14, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267822, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267876, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267899, "dur": 15, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267916, "dur": 14, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267931, "dur": 53, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528267986, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268001, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268020, "dur": 18, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268040, "dur": 16, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268058, "dur": 14, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268075, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268096, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268116, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268131, "dur": 16, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268150, "dur": 46, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268198, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268219, "dur": 16, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268236, "dur": 15, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268253, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268254, "dur": 15, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268272, "dur": 10, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268284, "dur": 67, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268353, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268370, "dur": 32, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268405, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268407, "dur": 14, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268425, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268444, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268504, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268527, "dur": 15, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268544, "dur": 16, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268563, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268584, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268601, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268618, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268635, "dur": 14, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268650, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268652, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268706, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268722, "dur": 53, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268776, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268778, "dur": 20, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268799, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268819, "dur": 14, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268836, "dur": 13, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268852, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268868, "dur": 59, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268930, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528268951, "dur": 53, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269007, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269009, "dur": 125, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269143, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269160, "dur": 349, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269512, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269542, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269545, "dur": 17, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269565, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269567, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269579, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269599, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269618, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269631, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269656, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269679, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269692, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269705, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269717, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269732, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269751, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269753, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269772, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269789, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269810, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269813, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269843, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269864, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269866, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269883, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269886, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269923, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269938, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269955, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269967, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528269991, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270005, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270032, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270052, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270054, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270070, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270072, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270083, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270085, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270116, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270136, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270138, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270157, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270171, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270187, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270200, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270220, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270233, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270254, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270276, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270279, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270294, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270310, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270312, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270334, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270354, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270375, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270378, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270400, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270422, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270436, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270458, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270460, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270475, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270489, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270491, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270508, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270534, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270547, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270584, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270603, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270617, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270619, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270638, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270657, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270683, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270698, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270764, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270777, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270828, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270846, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270868, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270903, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270920, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270921, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270948, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270964, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528270977, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271011, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271037, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271040, "dur": 11, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271054, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271069, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271070, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271090, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271105, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271125, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271128, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271149, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271166, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271184, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271186, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271204, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271218, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271232, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271245, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271257, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271260, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271281, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271283, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271299, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271301, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271318, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271346, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271358, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271385, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271406, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271408, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271425, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271427, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271444, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271461, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271475, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271489, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271527, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271570, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271573, "dur": 13, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271588, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271590, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271605, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271608, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271625, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271641, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271654, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271677, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271696, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271711, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271737, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271741, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271760, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271762, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271783, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271796, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271798, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271813, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271839, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271842, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271862, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271877, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271893, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271905, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271945, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271963, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271965, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528271985, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272016, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272036, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272038, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272055, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272073, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272089, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272106, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272109, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272131, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272146, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272148, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272163, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272196, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272213, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272216, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272234, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272250, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272252, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272269, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272283, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272300, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272318, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272346, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272360, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272377, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272399, "dur": 17, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272420, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272443, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272445, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272467, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272485, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272502, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272538, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272555, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272573, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272595, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272598, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272613, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272614, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272628, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272630, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272645, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272657, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272660, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272674, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272676, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272691, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272708, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272735, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272750, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272751, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272772, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272789, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272800, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272826, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272829, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272848, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272850, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272864, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272867, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272884, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272886, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272910, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272924, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272926, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272954, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272956, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272972, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272974, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528272993, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273008, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273026, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273052, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273054, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273071, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273087, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273105, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273123, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273135, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273159, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273189, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273202, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273233, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273247, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273272, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273287, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273289, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273304, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273335, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273359, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273361, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273379, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273406, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273421, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273434, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273458, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273460, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273478, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273481, "dur": 12, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273496, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273511, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273525, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273555, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273574, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273576, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273594, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273609, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273631, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273644, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273645, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273657, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273670, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273687, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273699, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273712, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273726, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273746, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273748, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273767, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273786, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273800, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273820, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273822, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273847, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273862, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273864, "dur": 10, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273876, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273901, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273924, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273927, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273961, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273980, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528273995, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274013, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274025, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274038, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274051, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274053, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274070, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274090, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274092, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274110, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274131, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274133, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274149, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274162, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274177, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274192, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274207, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274221, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274242, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274260, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274262, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274280, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274294, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274296, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274310, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274331, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274348, "dur": 9, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274359, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274399, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274415, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274429, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274431, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274448, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274460, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274490, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274509, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274520, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274522, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274535, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274548, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274576, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274578, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274595, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274611, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274624, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274645, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274659, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274675, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274687, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274698, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274717, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274719, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274742, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274756, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274772, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274792, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274794, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274811, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274830, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274842, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274854, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274873, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274875, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274892, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274893, "dur": 13, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274910, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274922, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274937, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274955, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274957, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274979, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528274992, "dur": 10, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275005, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275016, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275029, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275042, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275054, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275074, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275076, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275098, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275116, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275130, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275143, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275157, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275185, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275187, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275207, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275210, "dur": 14, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275228, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275243, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275256, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275268, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275269, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275286, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275309, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275330, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275333, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275347, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275513, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275531, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275564, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275578, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275595, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275598, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275629, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275646, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275662, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275680, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275701, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275704, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275721, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275723, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275737, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275738, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275756, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275812, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275833, "dur": 10, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275846, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275860, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275886, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275907, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275909, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275925, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275928, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275946, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275964, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275973, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528275990, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276009, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276023, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276025, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276065, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276076, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276138, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276156, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276170, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276172, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528276191, "dur": 7279, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528283478, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528283481, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528283505, "dur": 10210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528293722, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528293726, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528293745, "dur": 207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528293958, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528293960, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528293998, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294000, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294025, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294145, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294147, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294178, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294182, "dur": 548, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294734, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294736, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528294757, "dur": 15178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528309943, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528309948, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310029, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310031, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310074, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310076, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310097, "dur": 366, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310466, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310468, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310506, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310509, "dur": 313, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310825, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310827, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310885, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310887, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310906, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310986, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528310988, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311013, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311015, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311041, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311061, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311063, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311137, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311178, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311182, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311203, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311299, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311312, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311339, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311375, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311377, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311609, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311629, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311645, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311691, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311705, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311907, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311929, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311931, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311947, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311973, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311993, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528311997, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312016, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312043, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312045, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312090, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312114, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312134, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312136, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312158, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312160, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312193, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312246, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312261, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312420, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312422, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312446, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312467, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312481, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312520, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312553, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312580, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312608, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312610, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312634, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312649, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312652, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312696, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312719, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312721, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312831, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312846, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312936, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528312954, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313087, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313103, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313136, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313155, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313246, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313257, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313259, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313280, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313306, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313308, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313326, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313340, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313357, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313367, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313444, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313466, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313493, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313506, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313549, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313567, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313569, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313599, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313610, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313653, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313669, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313683, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313768, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313785, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313813, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313815, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313829, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313871, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313891, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313893, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313928, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313953, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528313957, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314004, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314028, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314048, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314072, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314074, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314095, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314110, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314138, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314262, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314271, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314287, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314302, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314341, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314357, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314386, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314454, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314471, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314493, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314508, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314559, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314577, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314630, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314649, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314675, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314688, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314747, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314765, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314766, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314789, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314801, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314832, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314850, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314867, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314883, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314897, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314919, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314936, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314963, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528314982, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315001, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315015, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315038, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315049, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315067, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315083, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315098, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315100, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315195, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315228, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315230, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315267, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315361, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315363, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315390, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315391, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315413, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315527, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315530, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315567, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315569, "dur": 85, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315658, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315661, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315696, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315698, "dur": 64, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315766, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315768, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315832, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315863, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528315866, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316006, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316032, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316060, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316096, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316098, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316169, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316206, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316239, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316435, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316437, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316467, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316469, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316542, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316571, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316573, "dur": 175, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316754, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316783, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316784, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316820, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316846, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316939, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316941, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528316966, "dur": 192, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317161, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317181, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317201, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317225, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317248, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317272, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317274, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317342, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528317361, "dur": 750, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318116, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318184, "dur": 3, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318189, "dur": 40, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318233, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318274, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318276, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318485, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318523, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318525, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318576, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318609, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318673, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318675, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318711, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318713, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318747, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318749, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318786, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318827, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528318856, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319033, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319035, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319070, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319072, "dur": 152, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319227, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319229, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319261, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319263, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319372, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319449, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319451, "dur": 117, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319583, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319622, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319624, "dur": 234, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319867, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528319910, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320168, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320228, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320231, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320282, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320284, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320318, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320374, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320379, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320425, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320629, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320662, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320874, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320909, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528320911, "dur": 120, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321035, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321037, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321079, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321112, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321114, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321211, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321213, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321429, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321464, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321620, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321625, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321657, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528321659, "dur": 482, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528322146, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528322164, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528322168, "dur": 51752, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528373936, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528373944, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528374011, "dur": 2006, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528376023, "dur": 5573, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528381605, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528381611, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528381642, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528381643, "dur": 717, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382365, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382368, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382388, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382528, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382531, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382586, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382588, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382633, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382656, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382676, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382688, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382859, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528382870, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528383056, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528383067, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528383122, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528383144, "dur": 848, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528383996, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528384019, "dur": 255, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528384277, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528384298, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528384301, "dur": 772, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385075, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385094, "dur": 288, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385385, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385400, "dur": 400, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385805, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385820, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385847, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385861, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385931, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528385946, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528386192, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528386208, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528386410, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528386423, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528386537, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528386553, "dur": 885, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387440, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387441, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387454, "dur": 336, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387793, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387813, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387815, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387846, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387848, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528387869, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388019, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388045, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388048, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388089, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388100, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388265, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388292, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388295, "dur": 549, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388847, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528388863, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528389055, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528389070, "dur": 601, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528389673, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528389689, "dur": 330, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390021, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390043, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390047, "dur": 347, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390396, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390421, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390667, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390678, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390786, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528390815, "dur": 296, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391114, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391129, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391183, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391202, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391203, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391337, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391350, "dur": 408, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391761, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391764, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391780, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528391811, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528392132, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528392134, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528392158, "dur": 1156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528393319, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528393339, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528393488, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528393513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528393516, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528393807, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528393820, "dur": 305, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394130, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394170, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394172, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394200, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394202, "dur": 453, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394659, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394682, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394696, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394765, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394794, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394796, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394907, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394929, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528394986, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395003, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395019, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395052, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395065, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395079, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395117, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395130, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395145, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395179, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395193, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395217, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395219, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395242, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395257, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395277, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395299, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395301, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395328, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395330, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395366, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395393, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395395, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395408, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395440, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395442, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395464, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395519, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395537, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395539, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395586, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395601, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395635, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395655, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395668, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395716, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395731, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395747, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395765, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395790, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395815, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395844, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395846, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395884, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395898, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395914, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395966, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395985, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528395987, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396003, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396065, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396087, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396089, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396129, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396141, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396163, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396194, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396314, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396331, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396345, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396359, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396375, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396394, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396396, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396416, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396432, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396450, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396464, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396484, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396486, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396503, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396519, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396534, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396555, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396557, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396571, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396585, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396593, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396595, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396618, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396642, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396666, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396688, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396704, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396722, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396724, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396742, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396762, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396780, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396795, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396817, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396834, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396851, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396866, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396887, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396904, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396918, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396938, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396957, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396959, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396986, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528396988, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397007, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397009, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397029, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397048, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397068, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397085, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397105, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397129, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397132, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397155, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397157, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397177, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397179, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397201, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397281, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397302, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397336, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397356, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397471, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397498, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397500, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397531, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397533, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397557, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397603, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397634, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397637, "dur": 103, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397743, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397745, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397781, "dur": 100, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397884, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397896, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528397923, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528398127, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528398128, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528398158, "dur": 219056, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528617224, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528617227, "dur": 140, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528617371, "dur": 18, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528617390, "dur": 57498, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528674896, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528674900, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159528674943, "dur": 461200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529136151, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529136155, "dur": 189, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529136348, "dur": 5, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529136357, "dur": 94030, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529230398, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529230401, "dur": 131, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529230536, "dur": 19, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529230557, "dur": 15666, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529246235, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529246241, "dur": 155, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529246398, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529246400, "dur": 15059, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529261468, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529261472, "dur": 152, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529261626, "dur": 25, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529261652, "dur": 13607, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529275268, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529275271, "dur": 139, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529275415, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529275419, "dur": 2707, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529278133, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529278135, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529278202, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529278223, "dur": 41461, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529319701, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529319709, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529319762, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529319764, "dur": 461308, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529781093, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529781104, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529781170, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529781177, "dur": 1435, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529782622, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529782628, "dur": 162, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529782798, "dur": 34, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529782835, "dur": 528, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529783367, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529783369, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529783408, "dur": 446, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754159529783857, "dur": 17644, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 36960, "tid": 64541, "ts": 1754159529817336, "dur": 2372, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 36960, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 36960, "tid": 8589934592, "ts": 1754159528239183, "dur": 87083, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 36960, "tid": 8589934592, "ts": 1754159528326268, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 36960, "tid": 8589934592, "ts": 1754159528326273, "dur": 906, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 36960, "tid": 64541, "ts": 1754159529819710, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 36960, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 36960, "tid": 4294967296, "ts": 1754159528219597, "dur": 1583259, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754159528224130, "dur": 7633, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754159529802981, "dur": 6402, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754159529805689, "dur": 189, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754159529809464, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 36960, "tid": 64541, "ts": 1754159529819723, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754159528242373, "dur": 2677, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159528245065, "dur": 951, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159528246171, "dur": 98, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754159528246269, "dur": 470, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159528247063, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4D750E9CBAFA47AF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754159528247902, "dur": 1380, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B2EEFE9B04C389F9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754159528249334, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_D2083F65E1F978AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754159528249955, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B4FAD1B5E9DC86B7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754159528264648, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754159528265286, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754159528246761, "dur": 21425, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159528268203, "dur": 1513827, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159529782031, "dur": 144, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159529782176, "dur": 120, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159529782323, "dur": 53, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159529782465, "dur": 63, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159529782550, "dur": 1256, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754159528246896, "dur": 21316, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528268233, "dur": 1492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528269735, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_DDA8E091E3967B34.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754159528269877, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528270207, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_CC1E7410E5880845.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754159528270338, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528270463, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528270529, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754159528270579, "dur": 1215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528271804, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528271886, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528272002, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528272078, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528272159, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528272267, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528272435, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528272562, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528273054, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528273138, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528273206, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528273693, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528273769, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528273869, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528273961, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528274025, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528274094, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528274223, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528274492, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754159528274545, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528274960, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528275083, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528275154, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528275840, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528276879, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528277530, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528278176, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528279564, "dur": 556, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Inspector\\PropertyDrawers\\Vector2PropertyDrawer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754159528278868, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528280470, "dur": 2978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528283449, "dur": 2872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528286321, "dur": 3741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528290063, "dur": 3228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528293292, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Analysis\\Analyser.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754159528293935, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\TMP_TextProcessingStack.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754159528293292, "dur": 3227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528296520, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528297646, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528298794, "dur": 2840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528301635, "dur": 3074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528304709, "dur": 1978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528306688, "dur": 1916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528308604, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528309125, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528309748, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754159528310025, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528310530, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528311573, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528311932, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528312752, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528313345, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528314071, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528314189, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754159528314445, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528314518, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528315146, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528315271, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528315809, "dur": 4292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528320102, "dur": 6331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528329186, "dur": 183, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1754159528329370, "dur": 935, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1754159528326434, "dur": 3924, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528330359, "dur": 47747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528378114, "dur": 3734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528381850, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528382294, "dur": 2470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528384765, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528385167, "dur": 3628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528388797, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528388909, "dur": 3327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528392237, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528392719, "dur": 3132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754159528395853, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528396250, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528396366, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528396425, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159528396730, "dur": 848465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159529245286, "dur": 73380, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754159529245200, "dur": 73469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754159529318672, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754159529318825, "dur": 463150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528247335, "dur": 20950, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528268295, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_674D8A39E28088FE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528268471, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528268600, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528268712, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528268815, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528268878, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_8784B94BF851BB6E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528268936, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269035, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269140, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269271, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_DF8D86BB7CEA9168.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528269322, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269424, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269527, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269625, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269724, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269815, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528269872, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_2BE1F67E8EB9FC15.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528269985, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528270091, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_C2658D8F62234B56.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528270142, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528270689, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528270764, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528270834, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528270902, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271007, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271077, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271298, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271433, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271506, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271584, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271700, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271813, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528271935, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528272036, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528272751, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528272876, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528273148, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528273395, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528273918, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528274051, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528274147, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528274441, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528274594, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528274967, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528275140, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528275208, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528275879, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528276713, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528277840, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528278533, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528279605, "dur": 561, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Input\\Texture\\ProceduralVirtualTextureNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754159528279571, "dur": 2216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528281787, "dur": 2648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528284436, "dur": 2787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528287224, "dur": 1970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528289195, "dur": 2998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528294054, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\Annotations\\AnnotationDisabler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754159528292193, "dur": 3966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528296159, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528297596, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528298770, "dur": 2993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528301764, "dur": 2552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528304316, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528304806, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528306692, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528307955, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528308603, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528309175, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528309758, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528310108, "dur": 804, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528310919, "dur": 1077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754159528311997, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528312298, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528312503, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528312591, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528312895, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528312993, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754159528314025, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528314118, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528314630, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528315270, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528315803, "dur": 1228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528317032, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754159528317195, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528317312, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754159528317817, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528317957, "dur": 2153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528320110, "dur": 58006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528378121, "dur": 3667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754159528381788, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528381891, "dur": 3410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754159528385303, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528385771, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754159528388214, "dur": 2129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528390349, "dur": 2880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754159528393230, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528393315, "dur": 3385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754159528396706, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528396770, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754159528396833, "dur": 1385127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528247675, "dur": 22215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528269958, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_03F87B6D11395095.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754159528270033, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528270172, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528270299, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528270509, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_C203261CAF21A327.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754159528270561, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528270689, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528270763, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528270842, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528270909, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528270980, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271068, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271134, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271205, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271309, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271384, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271452, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271532, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271610, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271679, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271765, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271836, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271906, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528271977, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528272047, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528272506, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528272578, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528272649, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528272720, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528272789, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528272946, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528273060, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528273141, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528273268, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528273362, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528273434, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528273988, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754159528274535, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528274871, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528274957, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528275072, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528275170, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528275247, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528275922, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528276707, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528277173, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528277635, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528278148, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528278681, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528279611, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Math\\Round\\RoundNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754159528279407, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528280515, "dur": 2892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528283408, "dur": 2643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528286051, "dur": 3499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528289550, "dur": 2789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528293060, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\DraggedListItem.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754159528294130, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Documentation\\XmlDocumentationTags.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754159528292339, "dur": 3601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528295940, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528297245, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528298354, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528300992, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\UnsafeBitArray.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754159528299381, "dur": 3834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528303215, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528304798, "dur": 2333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528307132, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528308579, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528309172, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528309744, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754159528309999, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528310067, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754159528310128, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528310729, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528311261, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528311322, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754159528311618, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528311754, "dur": 1765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528313520, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528313735, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528313808, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754159528314028, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528314112, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528314671, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528314779, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528315261, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528315804, "dur": 1663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528317469, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754159528317651, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528317795, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528318350, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528318602, "dur": 1505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528320107, "dur": 58013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528378130, "dur": 3429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528381561, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528381746, "dur": 3192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528384939, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528385043, "dur": 2962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528388016, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528388078, "dur": 3268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528391347, "dur": 2641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754159528393999, "dur": 3172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754159528397237, "dur": 1384721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528247025, "dur": 21197, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528268236, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528268797, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528269030, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528269150, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_3EC188A9A2685E71.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528269333, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528269452, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528269637, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_34C7DB8E4ECB1B7B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528269916, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E44D737DF0BCE080.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528270216, "dur": 1960, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528272208, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528272561, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528272687, "dur": 19712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528292401, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528292937, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528293322, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528293535, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528293964, "dur": 15034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528308999, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528309174, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528309291, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528309576, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528309717, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528309912, "dur": 948, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528310867, "dur": 1124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528311992, "dur": 1108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528313161, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528313606, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528314467, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528314622, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528314894, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528315702, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528315795, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528315971, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528316033, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528316606, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528316769, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528316958, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528317797, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528317900, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528317996, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754159528318213, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528318697, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528318825, "dur": 1298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528320124, "dur": 58010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528378136, "dur": 3262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528381399, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528381779, "dur": 3198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528384978, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528385085, "dur": 3186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528388271, "dur": 1353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528389652, "dur": 3236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528392889, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754159528393386, "dur": 3540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754159528396993, "dur": 1385080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528247045, "dur": 21184, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528268235, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B08776C80E53BDE9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528268362, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528268615, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528269109, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528269222, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528269336, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528269747, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528269842, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528270258, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_264AA3CB8F6F6771.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528270312, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528270766, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528270882, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528270952, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528271336, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528271400, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528271501, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528271624, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528271709, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528272374, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528272458, "dur": 409, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754159528272931, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273263, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273340, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273413, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273474, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273590, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273677, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273759, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273857, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528273949, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528274055, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528274181, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528274250, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528274409, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528274533, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528275078, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754159528275203, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528275301, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528276180, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528276880, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528277733, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528278394, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528279609, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Util\\StackPool.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754159528279198, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528280842, "dur": 2847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528283690, "dur": 2914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528286605, "dur": 2333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528288939, "dur": 4207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528293173, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528293229, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\BoltGUI.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754159528293780, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Assignment\\AssignsAttribute.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754159528293229, "dur": 3392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528296622, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528297606, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528298303, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528299501, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\GUIStyleState_DirectConverter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754159528299169, "dur": 3786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528302956, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528304758, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528305599, "dur": 2443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528308043, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528308566, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528309188, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528309750, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528309965, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528310060, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528310118, "dur": 2154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528312273, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528312539, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528312808, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528312884, "dur": 1814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528314752, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528314937, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528315052, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528315901, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528316168, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528316281, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528316393, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528316855, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528317025, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528317168, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528317246, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528317597, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528317716, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528317833, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528317900, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528318267, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754159528318411, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528318748, "dur": 1365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528320114, "dur": 57985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528378101, "dur": 2659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528380761, "dur": 1322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528382096, "dur": 3457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528385554, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528385646, "dur": 3503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528389150, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528389256, "dur": 3305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528392563, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528393035, "dur": 3701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754159528396738, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754159528396899, "dur": 1385068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528247186, "dur": 21078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528268283, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6926138A25866A37.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528268815, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528269039, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_34692D89E77A6B77.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528269269, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528269460, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_200C40F278319369.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528269562, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528269683, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_915E418E1D60FCA1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528269796, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528269999, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_7C8AF205DD25174E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528270106, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528270238, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_97F9292D53E5A600.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528270394, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528270482, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754159528270591, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528270787, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754159528270919, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528271067, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528271176, "dur": 409, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754159528271615, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528271763, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528271915, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528271977, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528272072, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528272151, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528272227, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528272331, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528272403, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754159528272653, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528272919, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528273047, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754159528273347, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528273429, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528273506, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528273584, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528273664, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528273725, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754159528273922, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528274024, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528274154, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528274224, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528274460, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528274563, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528275368, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@dcc61ebd6655\\Runtime\\Timeline\\CinemachineShotPlayable.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754159528275195, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528276264, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528276629, "dur": 962, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Editor\\TMP\\TMPro_SortingLayerHelper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754159528276629, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528278477, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528279605, "dur": 1001, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Math\\Advanced\\LogNode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754159528279532, "dur": 2370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528281902, "dur": 2405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528284307, "dur": 3036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528287343, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528288163, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528289754, "dur": 2616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528293140, "dur": 637, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Description\\MachineDescription.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754159528293777, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Description\\IMacroDescription.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754159528295824, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Dependencies\\SQLite\\SQLite.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754159528292370, "dur": 4153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528296523, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528297468, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528298235, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528299368, "dur": 3781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528303150, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528304931, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528306555, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528308041, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528308815, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528309139, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528309746, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528310099, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528310271, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754159528311088, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528311254, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528311570, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528311642, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754159528312685, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528313049, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528313271, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754159528314057, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528314172, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528314478, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528314625, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754159528314866, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754159528315517, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528315682, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528315817, "dur": 4288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528320106, "dur": 57996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528378103, "dur": 4144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754159528382248, "dur": 961, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528383222, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754159528385700, "dur": 1544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528387252, "dur": 3054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754159528390307, "dur": 3612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754159528393925, "dur": 3138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754159528397159, "dur": 1384927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528247400, "dur": 20936, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528268386, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528269018, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528269102, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_53F7BBBA74CCF99D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754159528269874, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BB19FC7FFBEB112B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754159528269979, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528270201, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528270804, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528270869, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754159528270962, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271096, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754159528271186, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271249, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754159528271302, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271410, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271481, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271553, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271631, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271707, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271779, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754159528271848, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271912, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528271980, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528272327, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528272400, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754159528272487, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528272573, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528272641, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528272715, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528272792, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528272880, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528272984, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528273063, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528273130, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528273198, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528273580, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528273656, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528273876, "dur": 1513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528275413, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528276038, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528276739, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528277664, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528278327, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528279141, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528280199, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528281474, "dur": 2766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528284240, "dur": 2526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528286766, "dur": 1923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528288689, "dur": 3604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528293918, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Special\\UnknownInspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754159528295724, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Special\\KeyValuePairInspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754159528292294, "dur": 4572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528296867, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528298016, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528300447, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Exceptions\\UnexpectedEnumValueException.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754159528298830, "dur": 2969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528301800, "dur": 2413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528304214, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528304922, "dur": 2313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528307236, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528308352, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528308579, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528309116, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528309753, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754159528309984, "dur": 1184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528311172, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528312307, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528312571, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754159528312881, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528314406, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528314631, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528315266, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528315802, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528316174, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754159528316437, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528316637, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528317176, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528317367, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754159528317465, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754159528317632, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528317733, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528318372, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528318606, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754159528318796, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528319268, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528319461, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528320116, "dur": 58007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528378126, "dur": 3126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528381253, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528381584, "dur": 2661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528384246, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528384310, "dur": 2863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528387173, "dur": 1108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528388286, "dur": 2613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528390900, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528390995, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528393222, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528393297, "dur": 3325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754159528396623, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528396710, "dur": 226804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159528623655, "dur": 508571, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754159528623525, "dur": 510145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754159529134715, "dur": 166, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754159529135749, "dur": 124876, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754159529274174, "dur": 506020, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754159529274160, "dur": 506036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754159529780230, "dur": 1606, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754159528247525, "dur": 21106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528268831, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_18A3D624D5A4F4D6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754159528268906, "dur": 764, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528269850, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528270157, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528270696, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754159528270817, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528270891, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528271017, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528271121, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528271480, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528271879, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528271938, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272024, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272443, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272538, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272608, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272672, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272736, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272823, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272896, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528272992, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273063, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273144, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273215, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273278, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273359, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273428, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273498, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273577, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273661, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273756, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528273871, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528274003, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528274081, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528274157, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528274322, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528274597, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528274878, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754159528275036, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528275170, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528275633, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528276077, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528277093, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528277670, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528278217, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528278751, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528279512, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528280289, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528281396, "dur": 2763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528284159, "dur": 3108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528287267, "dur": 2779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528290046, "dur": 3257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528293303, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\TMP_Sprite.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754159528293841, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\TMP_ShaderUtilities.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754159528293303, "dur": 2884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528296187, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528296913, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528297780, "dur": 824, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\OnMouseExit.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754159528297587, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528299209, "dur": 3332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528302542, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528304485, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528305262, "dur": 2285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528307548, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528308569, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528309171, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528309751, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754159528309989, "dur": 1045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754159528311034, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528311159, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754159528311382, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754159528312087, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528312389, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754159528312635, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528312728, "dur": 1109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754159528313838, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528314325, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528314623, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754159528314828, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754159528315258, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1754159528315940, "dur": 215, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528316607, "dur": 56485, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1754159528378095, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754159528380698, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528380785, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754159528383322, "dur": 3753, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528387081, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754159528389550, "dur": 863, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528390419, "dur": 3185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754159528393605, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528394639, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528395021, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528395128, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528395756, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528396057, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528396433, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754159528396872, "dur": 1385142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528247640, "dur": 21477, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528269161, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528269258, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528269433, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_8029272894F862A3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528269574, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528269633, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_D1C7B996A6C45BF4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528269821, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5F1525A9150C0C6C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528270144, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528270568, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528271178, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528271248, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528271439, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528271694, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528272200, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528272296, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528272359, "dur": 9883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528282243, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528282713, "dur": 2790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528285503, "dur": 3027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528288531, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528290425, "dur": 2787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528293226, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Context\\GraphContextProvider.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754159528293212, "dur": 2901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528296114, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528297217, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528298173, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528298914, "dur": 2823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528301738, "dur": 2662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528304401, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528304896, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528306563, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528308036, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528308904, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528309200, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528310011, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528310368, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528311157, "dur": 1712, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528312909, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528313758, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528313912, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528314105, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528314219, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528314318, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528314640, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528315264, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528315806, "dur": 2193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528318001, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528318202, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528318258, "dur": 1161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528319486, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528319581, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528320107, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754159528320250, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528320597, "dur": 57582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528378182, "dur": 3644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528381827, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528381913, "dur": 2312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528384227, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528384325, "dur": 2887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528387213, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528387321, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528389629, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528390053, "dur": 2659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754159528392713, "dur": 1882, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528394617, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528394769, "dur": 1103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528395881, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528396250, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528396305, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1754159528396443, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1754159528396500, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754159528396904, "dur": 1385032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528247871, "dur": 22156, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528270111, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528270289, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528270390, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528270477, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528270569, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528270652, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528270774, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528271305, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754159528271526, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528271676, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528272156, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528272333, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754159528272388, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528272585, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528273209, "dur": 1906, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528275172, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528275740, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528276484, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528277153, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528277799, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528278447, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528279603, "dur": 1009, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Util\\GradientUtil.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754159528279262, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528280793, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528283381, "dur": 2717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528286098, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528287940, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528289612, "dur": 2149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528293891, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\LudiqGUI.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754159528291762, "dur": 3412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528295174, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528296631, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528297787, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528298723, "dur": 2738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528301461, "dur": 2478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528303940, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528304703, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528305903, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528307599, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528308245, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528308509, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528308568, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528309118, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528310003, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754159528310196, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528310282, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528311073, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528311478, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754159528311686, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528311781, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528312822, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528313361, "dur": 869, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528314235, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528314641, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528315285, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528315797, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754159528315962, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528316445, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528316967, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528317252, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754159528317470, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528317985, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528318049, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528318425, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754159528318525, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528319004, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528319394, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754159528319480, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528319786, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528319851, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528320103, "dur": 10279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528330383, "dur": 47742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528378126, "dur": 2512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528380640, "dur": 1710, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528382359, "dur": 2932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528385293, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528385425, "dur": 3691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528389117, "dur": 776, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528389904, "dur": 3175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528393080, "dur": 795, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528393883, "dur": 2852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754159528396736, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754159528396875, "dur": 1385128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528247743, "dur": 22175, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528269920, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_82714E7E5E4D2D26.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754159528270255, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528270370, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528270440, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528270553, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528270668, "dur": 1501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528272253, "dur": 745, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528273183, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528273376, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528273468, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528274104, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528274232, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528274540, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754159528274598, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528274898, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528275175, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528275845, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528276793, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528277228, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528277690, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528278130, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528278683, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528279620, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Math\\Vector\\DistanceNode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754159528279323, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528280659, "dur": 2897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528283556, "dur": 2183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528285740, "dur": 2637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528288377, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528289691, "dur": 2615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528293124, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\MetadataDictionaryAdaptor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754159528293742, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\MacroEditor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754159528292307, "dur": 4010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528296318, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528297362, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528298323, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528299072, "dur": 3085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528302158, "dur": 1987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528304145, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528304665, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528305452, "dur": 2201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528307654, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528308511, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528308566, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528309128, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528309984, "dur": 1056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754159528311041, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528311359, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754159528312419, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528312834, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754159528313033, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528313262, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754159528313625, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528313717, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754159528314770, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528314909, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528315262, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528315822, "dur": 4276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528320100, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754159528320269, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528320349, "dur": 57760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528378113, "dur": 3065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754159528381179, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528381597, "dur": 4679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754159528386277, "dur": 763, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528387047, "dur": 3782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754159528390829, "dur": 512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528391350, "dur": 3376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754159528394727, "dur": 827, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528395584, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528395919, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528396316, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754159528396902, "dur": 1385033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528247907, "dur": 22072, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528270113, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528270304, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528270414, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528271111, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528271256, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528271562, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528271665, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272146, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272272, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272437, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272510, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754159528272585, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272658, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272732, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272811, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272889, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528272984, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528273057, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528273125, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528273190, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528273661, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528273760, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528273863, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528273960, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528274030, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528274102, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528274217, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528274309, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528274405, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528274533, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528274967, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528275045, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528275139, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528275244, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528275998, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528276615, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528277077, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528277867, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528278356, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528279065, "dur": 1554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SampleTexture2DNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754159528278907, "dur": 3471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528282379, "dur": 3408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528285787, "dur": 2099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528287886, "dur": 2216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528290102, "dur": 2354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528293825, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\ElementAdderMenu\\IElementAdderMenuCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754159528295893, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\ElementAdderMenu\\ElementAdderMenuCommandAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754159528292457, "dur": 4424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528296882, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528297780, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528298473, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528299334, "dur": 4289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528303624, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528303925, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528304303, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528304804, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528306105, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528307858, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528308489, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528308604, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528309136, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528309925, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754159528310344, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528310413, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754159528311172, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528311354, "dur": 704, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528312063, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754159528312985, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528313303, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528313795, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754159528314084, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754159528314714, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528314882, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528315261, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528315825, "dur": 4269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528320096, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754159528320302, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754159528320700, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528320819, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754159528321914, "dur": 294441, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754159528624211, "dur": 49614, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754159528623484, "dur": 50437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754159528673923, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159528674025, "dur": 458192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754159528674023, "dur": 459692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754159529135137, "dur": 172, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754159529135816, "dur": 93714, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754159529245190, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754159529245164, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754159529245377, "dur": 536658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528248023, "dur": 21947, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528269970, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A9644AD1997CC33F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754159528270030, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528270183, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_B42A413E5BA9606D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754159528270602, "dur": 1117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528271799, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528271858, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528272047, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528272747, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528272837, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528273247, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528273334, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528273399, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528273456, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528273573, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528274278, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528274514, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528275012, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528275398, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528275816, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528276627, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528277298, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528277962, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528279618, "dur": 991, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Generation\\Collections\\DefineCollection.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754159528278666, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528280610, "dur": 2662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528283273, "dur": 3181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528286455, "dur": 3563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528290019, "dur": 2283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528293340, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\UlongInspector.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754159528293882, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\StringInspector.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754159528292302, "dur": 3729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528296031, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528297228, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528298332, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528299153, "dur": 3142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528302295, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528304255, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528304774, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528305319, "dur": 1962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528307282, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528308032, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528308687, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528309202, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528310175, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754159528310465, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528310525, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754159528310598, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754159528312223, "dur": 955, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528313182, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754159528313496, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754159528314112, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528314196, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528314394, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528314628, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528314941, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754159528315051, "dur": 1290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754159528316342, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528316489, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754159528316659, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754159528317234, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528317347, "dur": 2768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528320115, "dur": 57980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528378112, "dur": 4700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754159528382813, "dur": 2218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528385038, "dur": 1973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754159528387012, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528387261, "dur": 3918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754159528391180, "dur": 1348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528392537, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754159528395329, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528395438, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528395648, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528395817, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528395934, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528396013, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528396183, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528396364, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528396424, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528396567, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754159528397301, "dur": 1384741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528247933, "dur": 22038, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528269972, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_FC9FF88BB6DDE854.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754159528270068, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_FC9FF88BB6DDE854.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754159528270239, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_0B408E8BC3EE7994.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754159528270675, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528270801, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528270881, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528271240, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528271319, "dur": 376, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754159528271816, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528272069, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528272889, "dur": 878, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528273809, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528273880, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754159528273952, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528274057, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528274213, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754159528274279, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528274464, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528274577, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528274925, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754159528275025, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528275160, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528275216, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528275693, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528276202, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528276983, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528277728, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528278406, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528279609, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Controls\\GradientControl.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754159528279133, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528280279, "dur": 2714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528282993, "dur": 2972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528285965, "dur": 2287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528288253, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528289597, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528293963, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Meta\\RootMetadata.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754159528291021, "dur": 3912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528294934, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528296601, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528297335, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528298508, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528299461, "dur": 4059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528303520, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528304679, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528305593, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528307958, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528308918, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528309180, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528309750, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754159528309981, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528310204, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754159528311500, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528311868, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754159528312168, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754159528313133, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528313312, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754159528313688, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754159528314374, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528314522, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528314637, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528315274, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528315799, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754159528315936, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754159528316242, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528316702, "dur": 3395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528320098, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754159528320263, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528320353, "dur": 57761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528378125, "dur": 3869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754159528381995, "dur": 2617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528384619, "dur": 2410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754159528387030, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528387500, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754159528390962, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528391026, "dur": 3893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754159528394921, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528395146, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528395240, "dur": 697, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528396151, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528396282, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754159528396450, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754159528396509, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754159528397039, "dur": 1385129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528248072, "dur": 21882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528269955, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_22A35CACB552B292.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754159528270184, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528270354, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528270978, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528271396, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528271457, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528271535, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528271914, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528272006, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528272342, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528272582, "dur": 929, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528273521, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528273712, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528273833, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528273926, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528274054, "dur": 1316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528275392, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528276081, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528277375, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528277862, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528278506, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528279288, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528280201, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528281029, "dur": 3076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528284105, "dur": 2357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528286462, "dur": 2278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528288740, "dur": 3449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528292189, "dur": 3858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528296048, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528297241, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528298109, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528298621, "dur": 2742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528301364, "dur": 2573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528303937, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528304473, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528305055, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528306872, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528307821, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528308812, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528309159, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528309762, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754159528309988, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528310060, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528310713, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528310863, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754159528311049, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528311124, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754159528311341, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754159528311700, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528312515, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528312680, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528313422, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528313573, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528313788, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754159528314184, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528314578, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528314732, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528315290, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528315796, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754159528315909, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528315977, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528316343, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528316437, "dur": 3667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528320104, "dur": 58000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528378105, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528380852, "dur": 2897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528383750, "dur": 2916, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528386672, "dur": 4137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528390810, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528390982, "dur": 3018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754159528394001, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528394524, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528394675, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528394817, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528395365, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528395739, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528395940, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528396410, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528396510, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754159528397169, "dur": 1384896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528248113, "dur": 21838, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528269952, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_61C77FC2752CA928.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754159528270097, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528270342, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528270455, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528270573, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528270683, "dur": 1353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528272047, "dur": 963, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528273011, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754159528273232, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528273310, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528273388, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528273454, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528273529, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528273599, "dur": 1398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528275024, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528275148, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528275828, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528276490, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528277024, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528277467, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528277921, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528278418, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528279601, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Procedural\\Shape\\RoundedPolygonNode.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754159528279314, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528280801, "dur": 2633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528283434, "dur": 2734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528286168, "dur": 3960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528290128, "dur": 3527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528294166, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\TMP_DefaultControls.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754159528295835, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\TMP_Character.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754159528293655, "dur": 3445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528297101, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528298232, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528299598, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsMemberSerialization.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754159528301500, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsConverterRegistrar.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754159528299166, "dur": 3913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528303079, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528304667, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528305533, "dur": 2383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528307963, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528308704, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528309194, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528309722, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754159528309882, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528310010, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528311013, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528311161, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528311423, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754159528311704, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528312488, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528312793, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528313630, "dur": 913, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528314558, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528314627, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754159528314806, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528315265, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528315430, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528315808, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754159528315974, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528316037, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528316629, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528316833, "dur": 3275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528320109, "dur": 58009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528378120, "dur": 4276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528382397, "dur": 1107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528383511, "dur": 3252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528386764, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528387028, "dur": 3458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528390487, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528390573, "dur": 2935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754159528393509, "dur": 1249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528394772, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528395202, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528395302, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528395409, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528395781, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528396071, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528396182, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528396325, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528396428, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528396773, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159528396830, "dur": 877429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754159529274318, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754159529274261, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754159529274434, "dur": 2867, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754159529277305, "dur": 504660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754159529797656, "dur": 2559, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 36960, "tid": 64541, "ts": 1754159529820177, "dur": 5330, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 36960, "tid": 64541, "ts": 1754159529825581, "dur": 2089, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 36960, "tid": 64541, "ts": 1754159529815571, "dur": 12999, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}