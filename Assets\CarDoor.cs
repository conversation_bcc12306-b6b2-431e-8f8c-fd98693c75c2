using System;
using System.Collections;
using UnityEngine;

public class CarDoor : MonoBehaviour
{
    [Header("Door Settings")]
    public bool isOpen = false;
    public float openAngle = 90f;
    public float openSpeed = 2f;
    public Transform door;

    private Vector3 closedRotation;
    private Vector3 openRotation;
    private bool playerInRange = false;

    void Start()
    {
        door = GetComponent<Transform>();
        // Store the initial rotation as the closed position
        closedRotation = door.eulerAngles;
        // Calculate the open rotation (rotate around Y-axis)
        openRotation = closedRotation + new Vector3(0, openAngle, 0);
    }

    void Update()
    {
        // Check for E key press when player is in range
        if (playerInRange && Input.GetKeyDown(KeyCode.E))
        {
            // Toggle door state
            isOpen = !isOpen;
        }

        // Smoothly rotate the door between open and closed positions
        Vector3 targetRotation = isOpen ? openRotation : closedRotation;
        door.eulerAngles = Vector3.Lerp(door.eulerAngles, targetRotation, openSpeed * Time.deltaTime);
        if (door.eulerAngles == openRotation)
        {
            isOpen = true;
            StartCoroutine(GetInCar());
        }
    }

    private IEnumerable GetInCar()
    {
        throw new NotImplementedException();
    }

    // Called when another collider enters the trigger
    void OnTriggerEnter(Collider other)
    {
        // Check if the colliding object has the "Player" tag
        if (other.CompareTag("Player"))
        {
            playerInRange = true;
        }
    }

    // Called when another collider exits the trigger
    void OnTriggerExit(Collider other)
    {
        // Check if the colliding object has the "Player" tag
        if (other.CompareTag("Player"))
        {
            playerInRange = false;
        }
    }
}
